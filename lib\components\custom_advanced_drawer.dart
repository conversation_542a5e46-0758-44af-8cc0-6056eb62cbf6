import 'package:business_app/plan/plan_overview.dart';
import 'package:business_app/ui/user_profiles/profile_page.dart';
import 'package:business_app/components/settings_and_privency.dart';
import 'package:business_app/ui/drawer_items/simple_notifications_page.dart';
import 'package:business_app/ui/drawer_items/my_shop/my_shop_page.dart';
import 'package:business_app/const/assets.dart';
import 'package:business_app/services/supabase_service.dart';
import 'package:business_app/models/auth/user_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_drawer/flutter_advanced_drawer.dart';
import 'package:rive_animated_icon/rive_animated_icon.dart';
import 'package:logger/logger.dart';

class CustomAdvancedDrawer extends StatefulWidget {
  final AdvancedDrawerController controller;
  final Widget child;

  const CustomAdvancedDrawer({
    super.key,
    required this.controller,
    required this.child,
  });

  @override
  State<CustomAdvancedDrawer> createState() => _CustomAdvancedDrawerState();
}

class _CustomAdvancedDrawerState extends State<CustomAdvancedDrawer> {
  UserModel? currentUser;
  bool isLoading = true;

  // Logger instance for proper logging
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: false,
    ),
  );

  @override
  void initState() {
    super.initState();
    _loadCurrentUserProfile();
  }

  Future<void> _loadCurrentUserProfile() async {
    try {
      final user = await AuthService.getCurrentUserProfile();
      if (mounted) {
        setState(() {
          currentUser = user;
          isLoading = false;
        });

        // Debug: Log profile image URL to verify database fetch
        if (user?.profileImageUrl != null) {
          _logger.d(
            '🖼️ Profile image URL from database: ${user!.profileImageUrl}',
          );
        } else {
          _logger.d(
            '🖼️ No profile image URL found in database, using grey avatar',
          );
        }
      }
    } catch (e) {
      _logger.e('❌ Error loading user profile: $e');
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return count.toString();
    }
  }

  Future<void> refreshUserProfile() async {
    setState(() {
      isLoading = true;
    });
    await _loadCurrentUserProfile();
  }

  Widget _buildProfileImage() {
    // Check if user has a profile image URL from database
    if (currentUser?.profileImageUrl != null &&
        currentUser!.profileImageUrl!.isNotEmpty) {
      return Image.network(
        currentUser!.profileImageUrl!,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Center(
            child: CircularProgressIndicator(
              value:
                  loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
              strokeWidth: 2,
              color: const Color(0xFF1DA1F2),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          // If network image fails, show grey avatar
          return _buildGreyAvatar();
        },
      );
    } else {
      // No profile image URL in database, show grey avatar
      return _buildGreyAvatar();
    }
  }

  Widget _buildGreyAvatar() {
    // Get initials from user name
    String initials = '';
    if (currentUser?.name != null && currentUser!.name.isNotEmpty) {
      final nameParts = currentUser!.name.trim().split(' ');
      if (nameParts.length >= 2) {
        initials = '${nameParts[0][0]}${nameParts[1][0]}'.toUpperCase();
      } else {
        initials = nameParts[0][0].toUpperCase();
      }
    } else {
      initials = 'U'; // Default for User
    }

    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: Colors.grey.shade300,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          initials,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade700,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // for pure dark & white👇
    // final isDark = Theme.of(context).brightness == Brightness.dark;
    return AdvancedDrawer(
      //for pure dark & white👇
      //backdropColor: isDark ? Colors.black : Colors.white,
      controller: widget.controller,
      animationCurve: Curves.easeInOut,
      animationDuration: const Duration(milliseconds: 300),
      backdrop: Container(
        // decoration: BoxDecoration(
        //   gradient: LinearGradient(
        //     colors: [
        //       Colors.black.withOpacity(0.8),
        //       Colors.black.withOpacity(0.6),
        //     ],
        //     begin: Alignment.topLeft,
        //     end: Alignment.bottomRight,
        //   ),
        // ),
      ),
      childDecoration: BoxDecoration(borderRadius: BorderRadius.circular(16)),
      drawer: SafeArea(
        child: ListTileTheme(
          // textColor: Colors.white,
          // iconColor: Colors.white,
          child: Column(
            children: [
              Expanded(
                child: RefreshIndicator(
                  onRefresh: refreshUserProfile,
                  color: const Color(0xFF1DA1F2),
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(
                      parent: BouncingScrollPhysics(),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 24.0),
                        // Dynamic User Profile Section
                        if (isLoading)
                          const Padding(
                            padding: EdgeInsets.symmetric(vertical: 20.0),
                            child: Center(
                              child: CircularProgressIndicator(
                                color: Color(0xFF1DA1F2),
                              ),
                            ),
                          )
                        else
                          InkWell(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => MyProfilePage(),
                                ),
                              );
                            },
                            borderRadius: BorderRadius.circular(30),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: 60,
                                  height: 60,
                                  clipBehavior: Clip.antiAlias,
                                  decoration: BoxDecoration(
                                    color: Colors.black26,
                                    shape: BoxShape.circle,
                                  ),
                                  child: _buildProfileImage(),
                                ),
                                const SizedBox(width: 16.0),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        currentUser?.name ?? 'Loading...',
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      Text(
                                        '@${currentUser?.username ?? 'loading'}',
                                        style: const TextStyle(
                                          fontSize: 14,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      const SizedBox(height: 8.0),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        // ...existing code...
                        // Profile Image and Name not navigation
                        // Row(
                        //   crossAxisAlignment: CrossAxisAlignment.center,
                        //   children: [
                        //     Container(
                        //       width: 60,
                        //       height: 60,
                        //       clipBehavior: Clip.antiAlias,
                        //       decoration: BoxDecoration(
                        //         color: Colors.black26,
                        //         shape: BoxShape.circle,
                        //       ),
                        //       child: Image.asset(Assets.newProfile),
                        //     ),
                        //     const SizedBox(width: 16.0),
                        //     Expanded(
                        //       child: Column(
                        //         crossAxisAlignment: CrossAxisAlignment.start,
                        //         children: const [
                        //           Text(
                        //             '꧁ŔEVER ⚘BC𓃵🛠️😊🛠️',
                        //             style: TextStyle(
                        //               fontSize: 16,
                        //               fontWeight: FontWeight.bold,
                        //               overflow: TextOverflow.ellipsis,
                        //               //color: Colors.white,
                        //             ),
                        //           ),
                        //           Text(
                        //             '@software_engineer',
                        //             style: TextStyle(
                        //               fontSize: 14,
                        //               // color: Colors.white70,
                        //               overflow: TextOverflow.ellipsis,
                        //             ),
                        //           ),
                        //           SizedBox(height: 8.0),
                        //         ],
                        //       ),
                        //     ),
                        //     Icon(
                        //       Icons.more_vert_rounded,
                        //       // color: Colors.white70,
                        //     ),
                        //   ],
                        // ),
                        const SizedBox(height: 16.0),
                        if (!isLoading)
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: [
                                Text(
                                  _formatCount(
                                    currentUser?.followersCount ?? 0,
                                  ),
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const Text(' Customers'),
                                const SizedBox(width: 16.0),
                                Text(
                                  _formatCount(
                                    currentUser?.followingCount ?? 0,
                                  ),
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const Text(' Following'),
                              ],
                            ),
                          ),

                        const SizedBox(height: 100.0),
                        const Divider(
                          // color: Colors.white54,
                          thickness: 0.3,
                          indent: 20.0,
                          endIndent: 20.0,
                        ),

                        //rive icon test
                        ListTile(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => MyProfilePage(),
                              ), // Replace with your target screen
                            );
                          },
                          leading: RiveAnimatedIcon(
                            riveIcon: RiveIcon.profile,
                            width: 40,
                            height: 40,
                            strokeWidth: 3,
                            color: Color(0xFF1DA1F2),
                            // color: Colors.white, // Adjust color as needed
                            loopAnimation: false,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => MyProfilePage(),
                                ), // Replace with your target screen
                              );
                            }, // Optional: Keep this if you want additional behavior
                            onHover: (value) {},
                          ),
                          title: const Text(
                            'Profile', // Title for the icon
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1DA1F2),
                              //color: Colors.white, // Adjust color as needed
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        /* ListTile(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => NewMessage(),
                            ), // Replace with your target screen
                          );
                        },
                        leading: RiveAnimatedIcon(
                          riveIcon: RiveIcon.message,
                          width: 40,
                          height: 40,
                          strokeWidth: 3,
                          //color: Colors.white, // Adjust color as needed
                          color: Color(0xFF1DA1F2),
                          loopAnimation: false,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => NewMessage(),
                              ), // Replace with your target screen
                            );
                          }, // Optional: Keep this if you want additional behavior
                          onHover: (value) {},
                        ),
                        title: const Text(
                          'Message', // Title for the icon
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(
                              0xFF1DA1F2,
                            ), // color: Colors.white, // Adjust color as needed
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),*/
                        ListTile(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => PlanOverviewPage(),
                              ), // Replace with your target screen
                            );
                          },
                          leading: ClipOval(
                            child: Image.asset(
                              Assets.trademateLogo,
                              width: 30.0,
                              height: 30.0,
                              fit: BoxFit.cover,
                            ),
                          ),

                          /* leading: RiveAnimatedIcon(
                          riveIcon: RiveIcon.diamond,
                          width: 40,
                          height: 40,
                          strokeWidth: 3,
                          //color: Colors.white, // Adjust color as needed
                          color: Color(0xFF1DA1F2),
                          loopAnimation: false,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ReelUI(),
                              ), // Replace with your target screen
                            );
                          }, // Optional: Keep this if you want additional behavior
                          onHover: (value) {},
                        ), */
                          title: const Text(
                            '  Free Plan', // Title for the icon
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Color(
                                0xFF1DA1F2,
                              ), // color: Colors.white, // Adjust color as needed
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        ListTile(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const NotificationsPage(),
                              ),
                            );
                          },
                          leading: Stack(
                            clipBehavior: Clip.none,
                            children: [
                              RiveAnimatedIcon(
                                riveIcon: RiveIcon.bell,
                                width: 40,
                                height: 40,
                                strokeWidth: 3,
                                color: Color(0xFF1DA1F2),
                                loopAnimation: false,
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder:
                                          (context) =>
                                              const NotificationsPage(),
                                    ),
                                  );
                                },
                                onHover: (value) {},
                              ),
                              Positioned(
                                top: -6,
                                right: -8,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 5,
                                    vertical: 1,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.redAccent,
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black26,
                                        blurRadius: 4,
                                        offset: Offset(0, 2),
                                      ),
                                    ],
                                    border: Border.all(
                                      color: Colors.white,
                                      width: 2,
                                    ),
                                  ),
                                  child: const Text(
                                    '16', // Notification count
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                      shadows: [
                                        Shadow(
                                          color: Colors.black38,
                                          blurRadius: 2,
                                          offset: Offset(0, 1),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          title: const Text(
                            'Notifications', // Title for the icon
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              //color: Colors.white, // Adjust color as needed
                              color: Color(0xFF1DA1F2),
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        ListTile(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const MyShopPage(),
                              ),
                            );
                          },
                          leading: const Icon(
                            Icons.store,
                            color: Color(0xFF1DA1F2),
                            size: 32,
                          ),
                          title: const Text(
                            'My Shop',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1DA1F2),
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        /* ListTile(
                        onTap: () {
                          // Navigate to the desired screen or perform an action
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ChatInterfaceUI(),
                            ), //Replace with your target screen
                          );
                        },
                        leading: RiveAnimatedIcon(
                          riveIcon: RiveIcon.star,
                          width: 40,
                          height: 40,
                          strokeWidth: 3,
                          color: Color(0xFF1DA1F2),
                          //color: Colors.white, // Adjust color as needed
                          loopAnimation: false,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => FirstChatInterfaceUI(),
                              ), //Replace with your target screen
                            );
                          }, // Optional: Keep this if you want additional behavior
                          onHover: (value) {},
                        ),
                        title: const Text(
                          "Vacancies", // Title for the icon
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF1DA1F2),
                            //color: Colors.white, // Adjust color as needed
                          ),
                        ),
                      ),*/
                        ListTile(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => SettingsAndPrivacy(),
                              ), // Replace with your target screen
                            );
                          },
                          leading: RiveAnimatedIcon(
                            riveIcon: RiveIcon.settings,
                            width: 40,
                            height: 40,
                            strokeWidth: 3,
                            //color: Colors.white,
                            color: Color(0xFF1DA1F2),
                            loopAnimation: false,
                            onTap:
                                () {}, // Optional: Keep this if you want additional behavior
                            onHover: (value) {},
                          ),
                          title: const Text(
                            'Settings', // Title for the icon
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              //color: Colors.white, // Adjust color as needed
                              color: Color(0xFF1DA1F2),
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                        /*  // User Existence Test (Debug only)
                      if (kDebugMode)
                        ListTile(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => const UserExistenceTestPage(),
                              ),
                            );
                          },
                          leading: const Icon(
                            Icons.bug_report,
                            color: Colors.orange,
                            size: 24,
                          ),
                          title: const Text(
                            'User Existence Test',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          subtitle: const Text(
                            'Debug: Test user existence check',
                            style: TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                        ),
                        */
                        const Divider(
                          //color: Colors.white54,
                          thickness: 0.3,
                          indent: 20.0,
                          endIndent: 20.0,
                        ),
                        //rive icon test end
                        // const Divider(
                        //   color: Colors.white54,
                        //   thickness: 1.0,
                        //   indent: 4.0,
                        //   endIndent: 4.0,
                        //   height: 30.0,
                        // ),
                      ],
                    ),
                  ),
                ),
              ),
              DefaultTextStyle(
                style: TextStyle(fontSize: 12 /*color: Colors.white54*/),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: Text('Terms of Service | Privacy Policy'),
                ),
              ),
            ],
          ),
        ),
      ),
      child: widget.child,
    );
  }

  Widget buildDrawerItem(String title, RiveIcon icon) {
    return ListTile(
      onTap: () {},
      leading: RiveAnimatedIcon(
        riveIcon: icon,
        width: 40,
        height: 40,
        strokeWidth: 3,
        color: Color(0xFF1DA1F2),
        //color: Colors.white70,
        loopAnimation: false,
        onTap: () {},
        onHover: (value) {},
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          //  color: Colors.white,
          color: Color(0xFF1DA1F2),
        ),
      ),
    );
  }
}
// This widget is used to create a custom advanced drawer with a profile section, navigation items, and a backdrop.
// It uses the AdvancedDrawer package to create a drawer that can be opened with a swipe gesture